version: "3.8"

services:
  flutter_proto_lib:
    build:
      dockerfile: manifest/docker/Dockerfile.build_sdk
    image: flutter_proto_lib
    volumes:
      - ./:/src
    working_dir: /src
    entrypoint: ["/bin/bash", "manifest/docker/build_proto_sdk.sh", "/src/flutter_proto_lib"]

  build_svc:
    image: golang:1.24-alpine
    volumes:
      - ./:/src
    working_dir: /src
    entrypoint: ["/bin/sh", "build.sh"]