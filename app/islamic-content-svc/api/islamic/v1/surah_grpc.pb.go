// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// SurahServiceClient is the client API for SurahService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SurahServiceClient interface {
	SurahList(ctx context.Context, in *SurahListReq, opts ...grpc.CallOption) (*SurahListRes, error)
	JuzList(ctx context.Context, in *JuzListReq, opts ...grpc.CallOption) (*JuzListRes, error)
	AyahList(ctx context.Context, in *AyahListReq, opts ...grpc.CallOption) (*AyahListRes, error)
	AyahReadRecord(ctx context.Context, in *AyahReadRecordReq, opts ...grpc.CallOption) (*AyahReadRecordRes, error)
	AyahReadRecordList(ctx context.Context, in *AyahReadRecordListReq, opts ...grpc.CallOption) (*AyahReadRecordListRes, error)
	CheckAyahReadCollectStatus(ctx context.Context, in *CheckAyahReadCollectStatusReq, opts ...grpc.CallOption) (*CheckAyahReadCollectStatusRes, error)
	AyahReadCollect(ctx context.Context, in *AyahReadCollectReq, opts ...grpc.CallOption) (*AyahReadCollectRes, error)
	AyahReadCollectList(ctx context.Context, in *AyahReadCollectListReq, opts ...grpc.CallOption) (*AyahReadCollectListRes, error)
	TahlilList(ctx context.Context, in *TahlilListReq, opts ...grpc.CallOption) (*TahlilListRes, error)
	WiridList(ctx context.Context, in *WiridListReq, opts ...grpc.CallOption) (*WiridListRes, error)
	WiridBacaanList(ctx context.Context, in *WiridBacaanListReq, opts ...grpc.CallOption) (*WiridBacaanListRes, error)
	DoaList(ctx context.Context, in *DoaListReq, opts ...grpc.CallOption) (*DoaListRes, error)
	DoaBacaanList(ctx context.Context, in *DoaBacaanListReq, opts ...grpc.CallOption) (*DoaBacaanListRes, error)
}

type surahServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSurahServiceClient(cc grpc.ClientConnInterface) SurahServiceClient {
	return &surahServiceClient{cc}
}

func (c *surahServiceClient) SurahList(ctx context.Context, in *SurahListReq, opts ...grpc.CallOption) (*SurahListRes, error) {
	out := new(SurahListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.SurahService/SurahList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) JuzList(ctx context.Context, in *JuzListReq, opts ...grpc.CallOption) (*JuzListRes, error) {
	out := new(JuzListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.SurahService/JuzList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) AyahList(ctx context.Context, in *AyahListReq, opts ...grpc.CallOption) (*AyahListRes, error) {
	out := new(AyahListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.SurahService/AyahList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) AyahReadRecord(ctx context.Context, in *AyahReadRecordReq, opts ...grpc.CallOption) (*AyahReadRecordRes, error) {
	out := new(AyahReadRecordRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.SurahService/AyahReadRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) AyahReadRecordList(ctx context.Context, in *AyahReadRecordListReq, opts ...grpc.CallOption) (*AyahReadRecordListRes, error) {
	out := new(AyahReadRecordListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.SurahService/AyahReadRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) CheckAyahReadCollectStatus(ctx context.Context, in *CheckAyahReadCollectStatusReq, opts ...grpc.CallOption) (*CheckAyahReadCollectStatusRes, error) {
	out := new(CheckAyahReadCollectStatusRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.SurahService/CheckAyahReadCollectStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) AyahReadCollect(ctx context.Context, in *AyahReadCollectReq, opts ...grpc.CallOption) (*AyahReadCollectRes, error) {
	out := new(AyahReadCollectRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.SurahService/AyahReadCollect", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) AyahReadCollectList(ctx context.Context, in *AyahReadCollectListReq, opts ...grpc.CallOption) (*AyahReadCollectListRes, error) {
	out := new(AyahReadCollectListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.SurahService/AyahReadCollectList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) TahlilList(ctx context.Context, in *TahlilListReq, opts ...grpc.CallOption) (*TahlilListRes, error) {
	out := new(TahlilListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.SurahService/TahlilList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) WiridList(ctx context.Context, in *WiridListReq, opts ...grpc.CallOption) (*WiridListRes, error) {
	out := new(WiridListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.SurahService/WiridList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) WiridBacaanList(ctx context.Context, in *WiridBacaanListReq, opts ...grpc.CallOption) (*WiridBacaanListRes, error) {
	out := new(WiridBacaanListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.SurahService/WiridBacaanList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) DoaList(ctx context.Context, in *DoaListReq, opts ...grpc.CallOption) (*DoaListRes, error) {
	out := new(DoaListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.SurahService/DoaList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) DoaBacaanList(ctx context.Context, in *DoaBacaanListReq, opts ...grpc.CallOption) (*DoaBacaanListRes, error) {
	out := new(DoaBacaanListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.SurahService/DoaBacaanList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SurahServiceServer is the server API for SurahService service.
// All implementations must embed UnimplementedSurahServiceServer
// for forward compatibility
type SurahServiceServer interface {
	SurahList(context.Context, *SurahListReq) (*SurahListRes, error)
	JuzList(context.Context, *JuzListReq) (*JuzListRes, error)
	AyahList(context.Context, *AyahListReq) (*AyahListRes, error)
	AyahReadRecord(context.Context, *AyahReadRecordReq) (*AyahReadRecordRes, error)
	AyahReadRecordList(context.Context, *AyahReadRecordListReq) (*AyahReadRecordListRes, error)
	CheckAyahReadCollectStatus(context.Context, *CheckAyahReadCollectStatusReq) (*CheckAyahReadCollectStatusRes, error)
	AyahReadCollect(context.Context, *AyahReadCollectReq) (*AyahReadCollectRes, error)
	AyahReadCollectList(context.Context, *AyahReadCollectListReq) (*AyahReadCollectListRes, error)
	TahlilList(context.Context, *TahlilListReq) (*TahlilListRes, error)
	WiridList(context.Context, *WiridListReq) (*WiridListRes, error)
	WiridBacaanList(context.Context, *WiridBacaanListReq) (*WiridBacaanListRes, error)
	DoaList(context.Context, *DoaListReq) (*DoaListRes, error)
	DoaBacaanList(context.Context, *DoaBacaanListReq) (*DoaBacaanListRes, error)
	mustEmbedUnimplementedSurahServiceServer()
}

// UnimplementedSurahServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSurahServiceServer struct {
}

func (UnimplementedSurahServiceServer) SurahList(context.Context, *SurahListReq) (*SurahListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurahList not implemented")
}
func (UnimplementedSurahServiceServer) JuzList(context.Context, *JuzListReq) (*JuzListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JuzList not implemented")
}
func (UnimplementedSurahServiceServer) AyahList(context.Context, *AyahListReq) (*AyahListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AyahList not implemented")
}
func (UnimplementedSurahServiceServer) AyahReadRecord(context.Context, *AyahReadRecordReq) (*AyahReadRecordRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AyahReadRecord not implemented")
}
func (UnimplementedSurahServiceServer) AyahReadRecordList(context.Context, *AyahReadRecordListReq) (*AyahReadRecordListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AyahReadRecordList not implemented")
}
func (UnimplementedSurahServiceServer) CheckAyahReadCollectStatus(context.Context, *CheckAyahReadCollectStatusReq) (*CheckAyahReadCollectStatusRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckAyahReadCollectStatus not implemented")
}
func (UnimplementedSurahServiceServer) AyahReadCollect(context.Context, *AyahReadCollectReq) (*AyahReadCollectRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AyahReadCollect not implemented")
}
func (UnimplementedSurahServiceServer) AyahReadCollectList(context.Context, *AyahReadCollectListReq) (*AyahReadCollectListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AyahReadCollectList not implemented")
}
func (UnimplementedSurahServiceServer) TahlilList(context.Context, *TahlilListReq) (*TahlilListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TahlilList not implemented")
}
func (UnimplementedSurahServiceServer) WiridList(context.Context, *WiridListReq) (*WiridListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WiridList not implemented")
}
func (UnimplementedSurahServiceServer) WiridBacaanList(context.Context, *WiridBacaanListReq) (*WiridBacaanListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WiridBacaanList not implemented")
}
func (UnimplementedSurahServiceServer) DoaList(context.Context, *DoaListReq) (*DoaListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DoaList not implemented")
}
func (UnimplementedSurahServiceServer) DoaBacaanList(context.Context, *DoaBacaanListReq) (*DoaBacaanListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DoaBacaanList not implemented")
}
func (UnimplementedSurahServiceServer) mustEmbedUnimplementedSurahServiceServer() {}

// UnsafeSurahServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SurahServiceServer will
// result in compilation errors.
type UnsafeSurahServiceServer interface {
	mustEmbedUnimplementedSurahServiceServer()
}

func RegisterSurahServiceServer(s *grpc.Server, srv SurahServiceServer) {
	s.RegisterService(&_SurahService_serviceDesc, srv)
}

func _SurahService_SurahList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurahListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).SurahList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.SurahService/SurahList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).SurahList(ctx, req.(*SurahListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_JuzList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JuzListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).JuzList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.SurahService/JuzList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).JuzList(ctx, req.(*JuzListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_AyahList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AyahListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).AyahList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.SurahService/AyahList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).AyahList(ctx, req.(*AyahListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_AyahReadRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AyahReadRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).AyahReadRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.SurahService/AyahReadRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).AyahReadRecord(ctx, req.(*AyahReadRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_AyahReadRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AyahReadRecordListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).AyahReadRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.SurahService/AyahReadRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).AyahReadRecordList(ctx, req.(*AyahReadRecordListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_CheckAyahReadCollectStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckAyahReadCollectStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).CheckAyahReadCollectStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.SurahService/CheckAyahReadCollectStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).CheckAyahReadCollectStatus(ctx, req.(*CheckAyahReadCollectStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_AyahReadCollect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AyahReadCollectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).AyahReadCollect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.SurahService/AyahReadCollect",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).AyahReadCollect(ctx, req.(*AyahReadCollectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_AyahReadCollectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AyahReadCollectListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).AyahReadCollectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.SurahService/AyahReadCollectList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).AyahReadCollectList(ctx, req.(*AyahReadCollectListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_TahlilList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TahlilListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).TahlilList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.SurahService/TahlilList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).TahlilList(ctx, req.(*TahlilListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_WiridList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WiridListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).WiridList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.SurahService/WiridList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).WiridList(ctx, req.(*WiridListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_WiridBacaanList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WiridBacaanListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).WiridBacaanList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.SurahService/WiridBacaanList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).WiridBacaanList(ctx, req.(*WiridBacaanListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_DoaList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoaListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).DoaList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.SurahService/DoaList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).DoaList(ctx, req.(*DoaListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_DoaBacaanList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoaBacaanListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).DoaBacaanList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.SurahService/DoaBacaanList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).DoaBacaanList(ctx, req.(*DoaBacaanListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _SurahService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.SurahService",
	HandlerType: (*SurahServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SurahList",
			Handler:    _SurahService_SurahList_Handler,
		},
		{
			MethodName: "JuzList",
			Handler:    _SurahService_JuzList_Handler,
		},
		{
			MethodName: "AyahList",
			Handler:    _SurahService_AyahList_Handler,
		},
		{
			MethodName: "AyahReadRecord",
			Handler:    _SurahService_AyahReadRecord_Handler,
		},
		{
			MethodName: "AyahReadRecordList",
			Handler:    _SurahService_AyahReadRecordList_Handler,
		},
		{
			MethodName: "CheckAyahReadCollectStatus",
			Handler:    _SurahService_CheckAyahReadCollectStatus_Handler,
		},
		{
			MethodName: "AyahReadCollect",
			Handler:    _SurahService_AyahReadCollect_Handler,
		},
		{
			MethodName: "AyahReadCollectList",
			Handler:    _SurahService_AyahReadCollectList_Handler,
		},
		{
			MethodName: "TahlilList",
			Handler:    _SurahService_TahlilList_Handler,
		},
		{
			MethodName: "WiridList",
			Handler:    _SurahService_WiridList_Handler,
		},
		{
			MethodName: "WiridBacaanList",
			Handler:    _SurahService_WiridBacaanList_Handler,
		},
		{
			MethodName: "DoaList",
			Handler:    _SurahService_DoaList_Handler,
		},
		{
			MethodName: "DoaBacaanList",
			Handler:    _SurahService_DoaBacaanList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/surah.proto",
}
