// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: pbentity/haji_jadwal_description.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HajiJadwalDescription struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                    // 主键ID
	Year          int32                  `protobuf:"varint,2,opt,name=Year,proto3" json:"Year,omitempty" dc:"朝觐年份"`                                // 朝觐年份
	LanguageId    uint32                 `protobuf:"varint,3,opt,name=LanguageId,proto3" json:"LanguageId,omitempty" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"` // 语言ID: 0-中文, 1-英文, 2-印尼语
	Description   string                 `protobuf:"bytes,4,opt,name=Description,proto3" json:"Description,omitempty" dc:"日程说明文字"`                 // 日程说明文字
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiJadwalDescription) Reset() {
	*x = HajiJadwalDescription{}
	mi := &file_pbentity_haji_jadwal_description_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiJadwalDescription) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiJadwalDescription) ProtoMessage() {}

func (x *HajiJadwalDescription) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_haji_jadwal_description_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiJadwalDescription.ProtoReflect.Descriptor instead.
func (*HajiJadwalDescription) Descriptor() ([]byte, []int) {
	return file_pbentity_haji_jadwal_description_proto_rawDescGZIP(), []int{0}
}

func (x *HajiJadwalDescription) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *HajiJadwalDescription) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *HajiJadwalDescription) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *HajiJadwalDescription) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

var File_pbentity_haji_jadwal_description_proto protoreflect.FileDescriptor

const file_pbentity_haji_jadwal_description_proto_rawDesc = "" +
	"\n" +
	"&pbentity/haji_jadwal_description.proto\x12\bpbentity\"}\n" +
	"\x15HajiJadwalDescription\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x04R\x02Id\x12\x12\n" +
	"\x04Year\x18\x02 \x01(\x05R\x04Year\x12\x1e\n" +
	"\n" +
	"LanguageId\x18\x03 \x01(\rR\n" +
	"LanguageId\x12 \n" +
	"\vDescription\x18\x04 \x01(\tR\vDescriptionB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_haji_jadwal_description_proto_rawDescOnce sync.Once
	file_pbentity_haji_jadwal_description_proto_rawDescData []byte
)

func file_pbentity_haji_jadwal_description_proto_rawDescGZIP() []byte {
	file_pbentity_haji_jadwal_description_proto_rawDescOnce.Do(func() {
		file_pbentity_haji_jadwal_description_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_haji_jadwal_description_proto_rawDesc), len(file_pbentity_haji_jadwal_description_proto_rawDesc)))
	})
	return file_pbentity_haji_jadwal_description_proto_rawDescData
}

var file_pbentity_haji_jadwal_description_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_haji_jadwal_description_proto_goTypes = []any{
	(*HajiJadwalDescription)(nil), // 0: pbentity.HajiJadwalDescription
}
var file_pbentity_haji_jadwal_description_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_haji_jadwal_description_proto_init() }
func file_pbentity_haji_jadwal_description_proto_init() {
	if File_pbentity_haji_jadwal_description_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_haji_jadwal_description_proto_rawDesc), len(file_pbentity_haji_jadwal_description_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_haji_jadwal_description_proto_goTypes,
		DependencyIndexes: file_pbentity_haji_jadwal_description_proto_depIdxs,
		MessageInfos:      file_pbentity_haji_jadwal_description_proto_msgTypes,
	}.Build()
	File_pbentity_haji_jadwal_description_proto = out.File
	file_pbentity_haji_jadwal_description_proto_goTypes = nil
	file_pbentity_haji_jadwal_description_proto_depIdxs = nil
}
