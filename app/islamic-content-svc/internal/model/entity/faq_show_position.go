// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// FaqShowPosition is the golang structure for table faq_show_position.
type FaqShowPosition struct {
	Id            uint `json:"id"            orm:"id"              description:""`        //
	FaqQuestionId uint `json:"faqQuestionId" orm:"faq_question_id" description:"faq id和"` // faq id和
	PositionId    int  `json:"positionId"    orm:"position_id"     description:"位置id"`    // 位置id
}
