// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// NewsWirid is the golang structure for table news_wirid.
type NewsWirid struct {
	Id         uint   `json:"id"         orm:"id"          description:""`               //
	Name       string `json:"name"       orm:"name"        description:"名称"`             // 名称
	Bacaans    uint   `json:"bacaans"    orm:"bacaans"     description:"数量"`             // 数量
	CreateTime int64  `json:"createTime" orm:"create_time" description:"创建时间（注册时间）"`     // 创建时间（注册时间）
	UpdateTime int64  `json:"updateTime" orm:"update_time" description:"更新时间，0代表创建后未更新"` // 更新时间，0代表创建后未更新
}
