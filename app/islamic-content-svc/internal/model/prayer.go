package model

// DailyPrayerTimeInput 祷告时间查询输入参数
type DailyPrayerTimeInput struct {
	Year           int     `json:"year" dc:"年份"`
	Month          int     `json:"month" dc:"月份"`
	Day            int     `json:"day" dc:"日期"`
	Latitude       float64 `json:"latitude" dc:"纬度"`
	Longitude      float64 `json:"longitude" dc:"经度"`
	Timezone       string  `json:"timezone" dc:"时区"`
	MethodCode     string  `json:"method_code" dc:"计算方法代码"`
	DateAdjustment int     `json:"date_adjustment" dc:"日期校正"`
}

// MonthlyPrayerTimesInput 祷告时间查询输入参数
type MonthlyPrayerTimesInput struct {
	Year           int     `json:"year" dc:"年份"`
	Month          int     `json:"month" dc:"月份"`
	Latitude       float64 `json:"latitude" dc:"纬度"`
	Longitude      float64 `json:"longitude" dc:"经度"`
	Timezone       string  `json:"timezone" dc:"时区"`
	MethodCode     string  `json:"method_code" dc:"计算方法代码"`
	DateAdjustment int     `json:"date_adjustment" dc:"日期校正"`
}

// PrayerTimeOutput 祷告时间查询输出结果
type PrayerTimeOutput struct {
	Date        string       `json:"date" dc:"日期"`
	PrayerTime  *PrayerTime  `json:"prayer_time" dc:"祷告时间"`
	IslamicDate *IslamicDate `json:"islamic_date" dc:"伊斯兰历日期"`
}

// PrayerTime 祷告时间
type PrayerTime struct {
	Imsak   string `json:"imsak" dc:"伊姆萨克时间（仅斋月期间）"`
	Subuh   string `json:"subuh" dc:"晨祷时间"`
	Terbit  string `json:"terbit" dc:"日出时间"`
	Dhuha   string `json:"dhuha" dc:"上午祷告时间（可选）"`
	Zuhur   string `json:"zuhur" dc:"晌祷时间"`
	Ashar   string `json:"ashar" dc:"晡祷时间"`
	Maghrib string `json:"maghrib" dc:"昏祷时间"`
	Isya    string `json:"isya" dc:"宵祷时间"`
}

// IslamicDate 伊斯兰历日期
type IslamicDate struct {
	Year  int32 `json:"year" dc:"伊斯兰历年"`
	Month int32 `json:"month" dc:"伊斯兰历月"`
	Day   int32 `json:"day" dc:"伊斯兰历日"`
}

// PrayerCalculationConfig 祷告时间计算配置
type PrayerCalculationConfig struct {
	Year      int32   `json:"year" dc:"年份"`
	Month     int32   `json:"month" dc:"月份"`
	Latitude  float64 `json:"latitude" dc:"用户位置纬度"`
	Longitude float64 `json:"longitude" dc:"用户位置经度"`
	Timezone  string  `json:"timezone" dc:"时区"`
}

// HajiJadwalInfo 朝觐日程信息
type HajiJadwalInfo struct {
	Id             uint64 `json:"id" dc:"朝觐日程ID"`
	ItemNo         int    `json:"item_no" dc:"项目编号"`
	TimeInfo       string `json:"time_info" dc:"时间信息"`
	EventSummary   string `json:"event_summary" dc:"事件简述"`
	AdditionalInfo string `json:"additional_info" dc:"附加信息"`
	ArticleText    string `json:"article_text" dc:"文章详情"`
	CreateTime     uint64 `json:"create_time" dc:"创建时间"`
	UpdateTime     uint64 `json:"update_time" dc:"更新时间"`
}

// HajiJadwalListOutput 朝觐日程列表输出
type HajiJadwalListOutput struct {
	List        []*HajiJadwalInfo `json:"list" dc:"朝觐日程列表"`
	Description string            `json:"description" dc:"日程说明文字"`
	Year        string            `json:"year" dc:"朝觐年份"`
}

// HajiJadwalDetailOutput 朝觐日程详情输出
type HajiJadwalDetailOutput struct {
	Jadwal *HajiJadwalInfo `json:"jadwal" dc:"朝觐日程详情"`
}
