// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// HajiJadwal is the golang structure of table haji_jadwal for DAO operations like Where/Data.
type HajiJadwal struct {
	g.Meta     `orm:"table:haji_jadwal, do:true"`
	Id         interface{} // 主键ID
	ItemNo     interface{} // 项目编号
	CreateTime interface{} // 创建时间（毫秒时间戳）
	UpdateTime interface{} // 更新时间（毫秒时间戳）
}
