// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// HajiJadwalContent is the golang structure of table haji_jadwal_content for DAO operations like Where/Data.
type HajiJadwalContent struct {
	g.Meta         `orm:"table:haji_jadwal_content, do:true"`
	Id             interface{} // 主键ID
	JadwalId       interface{} // 朝觐时间表ID，关联haji_jadwal.id
	LanguageId     interface{} // 语言ID: 0-中文, 1-英文, 2-印尼语
	TimeInfo       interface{} // 手动输入的时间信息
	EventSummary   interface{} // 事件简述
	AdditionalInfo interface{} // 附加信息
	ArticleText    interface{} // 文章详情（副文本）
	CreateTime     interface{} // 创建时间（毫秒时间戳）
	UpdateTime     interface{} // 更新时间（毫秒时间戳）
}
