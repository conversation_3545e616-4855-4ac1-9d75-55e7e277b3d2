// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// NewsDoaBacaan is the golang structure of table news_doa_bacaan for DAO operations like Where/Data.
type NewsDoaBacaan struct {
	g.Meta     `orm:"table:news_doa_bacaan, do:true"`
	Id         interface{} //
	DoaId      interface{} // doa_id
	Name       interface{} // 名称
	Content1   interface{} // 内容1
	Content2   interface{} // 内容2
	Content3   interface{} // 内容3
	CreateTime interface{} // 创建时间（注册时间）
	UpdateTime interface{} // 更新时间，0代表创建后未更新
}
