// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// HajiJadwalDescription is the golang structure of table haji_jadwal_description for DAO operations like Where/Data.
type HajiJadwalDescription struct {
	g.Meta      `orm:"table:haji_jadwal_description, do:true"`
	Id          interface{} // 主键ID
	Year        interface{} // 朝觐年份
	LanguageId  interface{} // 语言ID: 0-中文, 1-英文, 2-印尼语
	Description interface{} // 日程说明文字
}
