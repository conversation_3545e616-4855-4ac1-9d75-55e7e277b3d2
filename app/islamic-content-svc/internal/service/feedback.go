// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
)

type (
	IFeedback interface {
		FeedbackAdd(ctx context.Context, req *v1.FeedbackAddReq) (res *v1.FeedbackAddRes, err error)
	}
)

var (
	localFeedback IFeedback
)

func Feedback() IFeedback {
	if localFeedback == nil {
		panic("implement not found for interface IFeedback, forgot register?")
	}
	return localFeedback
}

func RegisterFeedback(i IFeedback) {
	localFeedback = i
}
