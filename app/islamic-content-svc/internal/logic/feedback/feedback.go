package feedback

import (
	"context"
	"github.com/gogf/gf/v2/os/gtime"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/dao"
	"halalplus/app/islamic-content-svc/internal/model/do"
	"halalplus/app/islamic-content-svc/internal/service"
	"halalplus/utility/token"
)

type sFeedback struct{}

func init() {
	service.RegisterFeedback(New())
}

func New() service.IFeedback {
	return &sFeedback{}
}

func (s *sFeedback) FeedbackAdd(ctx context.Context, req *v1.FeedbackAddReq) (res *v1.FeedbackAddRes, err error) {
	userId, err := token.GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}
	_, err = dao.Feedback.Ctx(ctx).Data(do.Feedback{
		UserId:       userId,
		FeedbackType: req.FeedbackType,
		Desc:         req.Desc,
		Images:       req.Images,
		FeedbackTime: gtime.New().UnixMilli(),
	}).Insert()
	return
}
