package prayer

import (
	"context"
	"fmt"
	"halalplus/app/islamic-content-svc/internal/model/entity"
	"time"

	"halalplus/app/islamic-content-svc/internal/dao"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/service"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/hablullah/go-prayer"
)

type sPrayer struct {
	cache *HijriahCache
}

func init() {
	service.RegisterPrayer(New())
}

func New() service.IPrayer {
	cache := &HijriahCache{
		data:     make(map[string]map[string]*entity.CalendarHijriah),
		stopChan: make(chan struct{}),
	}

	p := &sPrayer{
		cache: cache,
	}

	// 启动缓存
	go p.cache.start()
	return p
}

// GetDailyPrayerTime 获取每日祷告时间
func (s *sPrayer) GetDailyPrayerTime(ctx context.Context, in *model.DailyPrayerTimeInput) (*model.PrayerTimeOutput, error) {
	// 计算祷告时间
	monthlyPrayerTimes, err := s.calculateMonthlyPrayerTimes(ctx, in.Year, in.Month, in.Latitude, in.Longitude, in.Timezone)
	if err != nil {
		return nil, err
	}

	prayerTime := s.getPrayerTimeByDay(monthlyPrayerTimes, in.Day)
	if prayerTime == nil {
		return nil, gerror.New("prayer time not found")
	}

	// 转换为伊斯兰历
	date := fmt.Sprintf("%d-%02d-%02d", in.Year, in.Month, in.Day)
	islamicDate, err := s.convertToIslamicDate(date, in.MethodCode, int32(in.DateAdjustment))
	if err != nil {
		g.Log().Warning(ctx, "failed to convert to islamic date:", err)
		islamicDate = &model.IslamicDate{}
	}

	return &model.PrayerTimeOutput{
		Date:        date,
		PrayerTime:  prayerTime,
		IslamicDate: islamicDate,
	}, nil
}

// GetMonthlyPrayerTimes 获取月度祷告时间
func (s *sPrayer) GetMonthlyPrayerTimes(ctx context.Context, in *model.MonthlyPrayerTimesInput) ([]*model.PrayerTimeOutput, error) {

	// 计算祷告时间
	monthlyPrayerTimes, err := s.calculateMonthlyPrayerTimes(ctx, in.Year, in.Month, in.Latitude, in.Longitude, in.Timezone)
	if err != nil {
		return nil, err
	}

	outputs := make([]*model.PrayerTimeOutput, 0)
	for i, prayerTime := range monthlyPrayerTimes {
		// 转换为伊斯兰历
		date := fmt.Sprintf("%d-%02d-%02d", in.Year, in.Month, i+1)
		islamicDate, err := s.convertToIslamicDate(date, in.MethodCode, int32(in.DateAdjustment))
		if err != nil {
			g.Log().Warning(ctx, "failed to convert to islamic date:", err)
			islamicDate = &model.IslamicDate{}
		}

		outputs = append(outputs, &model.PrayerTimeOutput{
			Date:        date,
			PrayerTime:  prayerTime,
			IslamicDate: islamicDate,
		})
	}

	return outputs, nil
}

func (s *sPrayer) getPrayerTimeByDay(times []*model.PrayerTime, day int) *model.PrayerTime {
	if day >= 1 && day <= len(times) {
		return times[day-1]
	}
	return nil
}

// calculateMonthlyPrayerTimes 计算月度祷告时间
func (s *sPrayer) calculateMonthlyPrayerTimes(ctx context.Context, year int, month int, latitude float64, longitude float64, timezone string) ([]*model.PrayerTime, error) {
	// 解析时区
	loc, err := time.LoadLocation(timezone)
	if err != nil {
		g.Log().Warning(ctx, "failed to load timezone:", timezone, err)
		loc = time.UTC
	}

	schedules, err := prayer.Calculate(prayer.Config{
		Latitude:           latitude,
		Longitude:          longitude,
		Timezone:           loc,
		TwilightConvention: prayer.MWL(),
		AsrConvention:      prayer.Shafii,
		PreciseToSeconds:   true,
		Corrections: prayer.ScheduleCorrections{
			Fajr:    time.Duration(0) * time.Second,
			Sunrise: time.Duration(0) * time.Second,
			Zuhr:    time.Duration(2) * time.Minute,
			Asr:     time.Duration(0) * time.Second,
			Maghrib: time.Duration(0) * time.Second,
			Isha:    time.Duration(0) * time.Second,
		},
	}, year)
	if err != nil {
		return nil, err
	}

	outputs := make([]*model.PrayerTime, 0)
	for i := 1; i <= 31; i++ {
		// 这里下标31不怕的，下面匹配不到就会跳过了
		// 找到指定日期的祷告时间
		var daySchedule prayer.Schedule
		found := false
		targetDate := fmt.Sprintf("%d-%02d-%02d", year, month, i)
		for _, schedule := range schedules {
			if schedule.Date == targetDate {
				daySchedule = schedule
				found = true
				break
			}
		}

		if !found {
			break
		}

		// 格式化时间
		formatTime := func(t time.Time) string {
			return t.Round(time.Minute).Format("15:04")
		}

		prayerTime := &model.PrayerTime{
			Imsak:   formatTime(daySchedule.Fajr.Add(-10 * time.Minute)),
			Subuh:   formatTime(daySchedule.Fajr),
			Terbit:  formatTime(daySchedule.Sunrise),
			Zuhur:   formatTime(daySchedule.Zuhr),
			Ashar:   formatTime(daySchedule.Asr),
			Maghrib: formatTime(daySchedule.Maghrib),
			Isya:    formatTime(daySchedule.Isha),
		}

		// 计算Dhuha时间（日出后15-20分钟）
		dhuhaTime := daySchedule.Sunrise.Add(25 * time.Minute) // TODO: 不是很准，没完全跟nu online对齐
		prayerTime.Dhuha = formatTime(dhuhaTime)

		outputs = append(outputs, prayerTime)
	}

	return outputs, nil
}

// GetHajiJadwalList 获取朝觐日程列表
func (s *sPrayer) GetHajiJadwalList(ctx context.Context, languageId uint) (*model.HajiJadwalListOutput, error) {
	var jadwalList []*model.HajiJadwalInfo
	err := dao.HajiJadwal.Ctx(ctx).As("hj").
		Fields("hj.id, hj.item_no, hjc.time_info, hjc.event_summary, '' as additional_info, '' as article_text").
		LeftJoin(dao.HajiJadwalContent.Table()+" hjc", "hjc.jadwal_id = hj.id").
		Where("hjc.language_id", languageId).
		Order("hj.item_no ASC").
		Scan(&jadwalList)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐日程列表失败:", err)
		return nil, err
	}

	// 查询年份描述信息
	// 按照当前的伊斯兰历来查吧
	var descResult struct {
		Description string `json:"description"`
		Year        int    `json:"year"`
	}

	err = dao.HajiJadwalDescription.Ctx(ctx).
		Fields("description, year").
		Where(dao.HajiJadwalDescription.Columns().LanguageId, languageId).
		OrderDesc(dao.HajiJadwalDescription.Columns().Year).
		Limit(1).
		Scan(&descResult)

	var description string
	var year string
	if err != nil {
		g.Log().Warning(ctx, "查询朝觐日程描述失败:", err)
		// 不影响主要功能，使用默认值
		description = ""
		year = "2025"
	} else {
		description = descResult.Description
		year = fmt.Sprintf("%d", descResult.Year)
	}

	return &model.HajiJadwalListOutput{
		List:        jadwalList,
		Description: description,
		Year:        year,
	}, nil
}

// GetHajiJadwalDetail 获取朝觐日程详情
func (s *sPrayer) GetHajiJadwalDetail(ctx context.Context, jadwalId uint64, languageId uint) (*model.HajiJadwalDetailOutput, error) {
	// 查询朝觐日程详情，联合查询多语言内容
	var jadwal model.HajiJadwalInfo
	err := dao.HajiJadwal.Ctx(ctx).As("hj").
		Fields("hj.id, hj.item_no, hjc.time_info, hjc.event_summary, hjc.additional_info, hjc.article_text").
		LeftJoin(dao.HajiJadwalContent.Table()+" hjc", "hjc.jadwal_id = hj.id").
		Where("hj.id", jadwalId).
		Where("hjc.language_id", languageId).
		Scan(&jadwal)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐日程详情失败:", err)
		return nil, err
	}

	if jadwal.Id == 0 {
		return nil, gerror.New("not found")
	}

	return &model.HajiJadwalDetailOutput{
		Jadwal: &jadwal,
	}, nil
}
