package faq

import (
	"context"
	"halalplus/api/common"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/dao"
	"halalplus/app/islamic-content-svc/internal/model/entity"
	"halalplus/app/islamic-content-svc/internal/service"
	"halalplus/utility/token"
)

type sFaq struct{}

func init() {
	service.RegisterFaq(New())
}

func New() service.IFaq {
	return &sFaq{}
}

var cl = dao.FaqCateLanguage.Columns()

func (s *sFaq) CateList(ctx context.Context, languageId uint32) ([]*v1.FaqCateItem, error) {
	LanguageType := token.GetLanguageId(ctx)
	var items []*v1.FaqCateItem
	err := dao.FaqCate.Ctx(ctx).
		Fields("faq_cate.*, fcl.title, fcl.language_id").
		LeftJoin(dao.FaqCateLanguage.Table()+" fcl", "fcl.faq_cate_id = faq_cate.id").
		//Where("faq_cate.is_open = ?", 1).
		Where("fcl.language_id = ?", LanguageType).
		Order("faq_cate.sort Desc").
		Scan(&items)

	if err != nil {
		return nil, err
	}
	return items, nil

}

func (s *sFaq) FaqList(ctx context.Context, req *v1.FaqListByCateIdReq) (*v1.FaqListByCateIdData, error) {
	var items []*v1.FaqQuestionItem
	LanguageType := token.GetLanguageId(ctx)
	md := dao.FaqQuestion.Ctx(ctx).
		Fields("faq_question.*, fql.title, fql.desc, fql.language_id").
		LeftJoin(dao.FaqQuestionLanguage.Table()+" fql", "fql.faq_question_id = faq_question.id").
		LeftJoin(dao.FaqShowPosition.Table()+" fsp", "fsp.faq_question_id = fq.id").
		Where("faq_question.is_open = ?", 1).
		Where("faq_question.faq_cate_id = ?", req.CateId).
		Where("fsp.position_id = ?", req.PositionId).
		Where("fql.language_id = ?", LanguageType)
	if req.Keyword != "" {
		md = md.Where("fql.title like ?", "%"+req.Keyword+"%")
	}
	total, _ := md.Count()

	err := md.Order("faq_question.sort Desc").Page(int(req.Page.Page), int(req.Page.Size)).
		Scan(&items)
	if err != nil {
		return nil, err
	}

	out := &v1.FaqListByCateIdData{}
	out.List = items
	out.Page = &common.PageResponse{
		Page:  req.Page.Page,
		Size:  req.Page.Size,
		Total: int32(total),
	}

	return out, nil

}

func (s *sFaq) FaqOne(ctx context.Context, languageId uint32, id uint32) (*v1.FaqQuestionOneItem, error) {
	var faqQuestionOneItem *v1.FaqQuestionOneItem
	LanguageType := token.GetLanguageId(ctx)
	err := dao.FaqQuestion.Ctx(ctx).
		Fields("faq_question.*, fql.title, fql.desc, fql.language_id").
		LeftJoin(dao.FaqQuestionLanguage.Table()+" fql", "fql.faq_question_id = faq_question.id").
		//Where("faq_question.is_open = ?", 1).
		Where("fql.language_id = ?", LanguageType).
		Where("id = ?", id).
		Scan(&faqQuestionOneItem)
	if err != nil {
		return nil, err
	}
	if faqQuestionOneItem == nil {
		return nil, nil
	}
	// 查询 分类
	cateId := faqQuestionOneItem.FaqCateId
	var cate *entity.FaqCateLanguage
	err = dao.FaqCateLanguage.Ctx(ctx).Where(cl.FaqCateId, cateId).Where(cl.LanguageId, languageId).Scan(&cate)
	if err != nil {
		return nil, err
	}
	if cate != nil {
		faqQuestionOneItem.FaqCateTitle = cate.Title
	}

	return faqQuestionOneItem, nil
}
