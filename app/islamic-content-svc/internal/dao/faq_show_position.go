// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// faqShowPositionDao is the data access object for the table faq_show_position.
// You can define custom methods on it to extend its functionality as needed.
type faqShowPositionDao struct {
	*internal.FaqShowPositionDao
}

var (
	// FaqShowPosition is a globally accessible object for table faq_show_position operations.
	FaqShowPosition = faqShowPositionDao{internal.NewFaqShowPositionDao()}
)

// Add your custom methods and functionality below.
