// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// HajiJadwalDescriptionDao is the data access object for the table haji_jadwal_description.
type HajiJadwalDescriptionDao struct {
	table    string                       // table is the underlying table name of the DAO.
	group    string                       // group is the database configuration group name of the current DAO.
	columns  HajiJadwalDescriptionColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler           // handlers for customized model modification.
}

// HajiJadwalDescriptionColumns defines and stores column names for the table haji_jadwal_description.
type HajiJadwalDescriptionColumns struct {
	Id          string // 主键ID
	Year        string // 朝觐年份
	LanguageId  string // 语言ID: 0-中文, 1-英文, 2-印尼语
	Description string // 日程说明文字
}

// hajiJadwalDescriptionColumns holds the columns for the table haji_jadwal_description.
var hajiJadwalDescriptionColumns = HajiJadwalDescriptionColumns{
	Id:          "id",
	Year:        "year",
	LanguageId:  "language_id",
	Description: "description",
}

// NewHajiJadwalDescriptionDao creates and returns a new DAO object for table data access.
func NewHajiJadwalDescriptionDao(handlers ...gdb.ModelHandler) *HajiJadwalDescriptionDao {
	return &HajiJadwalDescriptionDao{
		group:    "default",
		table:    "haji_jadwal_description",
		columns:  hajiJadwalDescriptionColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *HajiJadwalDescriptionDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *HajiJadwalDescriptionDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *HajiJadwalDescriptionDao) Columns() HajiJadwalDescriptionColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *HajiJadwalDescriptionDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *HajiJadwalDescriptionDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *HajiJadwalDescriptionDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
