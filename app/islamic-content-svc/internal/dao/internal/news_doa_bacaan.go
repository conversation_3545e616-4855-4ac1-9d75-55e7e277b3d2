// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// NewsDoaBacaanDao is the data access object for the table news_doa_bacaan.
type NewsDoaBacaanDao struct {
	table    string               // table is the underlying table name of the DAO.
	group    string               // group is the database configuration group name of the current DAO.
	columns  NewsDoaBacaanColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler   // handlers for customized model modification.
}

// NewsDoaBacaanColumns defines and stores column names for the table news_doa_bacaan.
type NewsDoaBacaanColumns struct {
	Id         string //
	DoaId      string // doa_id
	Name       string // 名称
	Content1   string // 内容1
	Content2   string // 内容2
	Content3   string // 内容3
	CreateTime string // 创建时间（注册时间）
	UpdateTime string // 更新时间，0代表创建后未更新
}

// newsDoaBacaanColumns holds the columns for the table news_doa_bacaan.
var newsDoaBacaanColumns = NewsDoaBacaanColumns{
	Id:         "id",
	DoaId:      "doa_id",
	Name:       "name",
	Content1:   "content1",
	Content2:   "content2",
	Content3:   "content3",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewNewsDoaBacaanDao creates and returns a new DAO object for table data access.
func NewNewsDoaBacaanDao(handlers ...gdb.ModelHandler) *NewsDoaBacaanDao {
	return &NewsDoaBacaanDao{
		group:    "default",
		table:    "news_doa_bacaan",
		columns:  newsDoaBacaanColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *NewsDoaBacaanDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *NewsDoaBacaanDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *NewsDoaBacaanDao) Columns() NewsDoaBacaanColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *NewsDoaBacaanDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *NewsDoaBacaanDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *NewsDoaBacaanDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
