// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// HajiJadwalContentDao is the data access object for the table haji_jadwal_content.
type HajiJadwalContentDao struct {
	table    string                   // table is the underlying table name of the DAO.
	group    string                   // group is the database configuration group name of the current DAO.
	columns  HajiJadwalContentColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler       // handlers for customized model modification.
}

// HajiJadwalContentColumns defines and stores column names for the table haji_jadwal_content.
type HajiJadwalContentColumns struct {
	Id             string // 主键ID
	JadwalId       string // 朝觐时间表ID，关联haji_jadwal.id
	LanguageId     string // 语言ID: 0-中文, 1-英文, 2-印尼语
	TimeInfo       string // 手动输入的时间信息
	EventSummary   string // 事件简述
	AdditionalInfo string // 附加信息
	ArticleText    string // 文章详情（副文本）
	CreateTime     string // 创建时间（毫秒时间戳）
	UpdateTime     string // 更新时间（毫秒时间戳）
}

// hajiJadwalContentColumns holds the columns for the table haji_jadwal_content.
var hajiJadwalContentColumns = HajiJadwalContentColumns{
	Id:             "id",
	JadwalId:       "jadwal_id",
	LanguageId:     "language_id",
	TimeInfo:       "time_info",
	EventSummary:   "event_summary",
	AdditionalInfo: "additional_info",
	ArticleText:    "article_text",
	CreateTime:     "create_time",
	UpdateTime:     "update_time",
}

// NewHajiJadwalContentDao creates and returns a new DAO object for table data access.
func NewHajiJadwalContentDao(handlers ...gdb.ModelHandler) *HajiJadwalContentDao {
	return &HajiJadwalContentDao{
		group:    "default",
		table:    "haji_jadwal_content",
		columns:  hajiJadwalContentColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *HajiJadwalContentDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *HajiJadwalContentDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *HajiJadwalContentDao) Columns() HajiJadwalContentColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *HajiJadwalContentDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *HajiJadwalContentDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *HajiJadwalContentDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
