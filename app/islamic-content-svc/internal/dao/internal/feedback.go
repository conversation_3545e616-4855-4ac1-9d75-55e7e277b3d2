// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FeedbackDao is the data access object for the table feedback.
type FeedbackDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  FeedbackColumns    // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// FeedbackColumns defines and stores column names for the table feedback.
type FeedbackColumns struct {
	Id              string //
	FeedbackType    string // 反馈类型  1 系统故障 2 功能建议
	UserId          string // 用户id
	UserAccount     string // 用户账号
	Images          string // 反馈图片 json字符串
	Desc            string // 反馈内容
	FeedbackTime    string // 反馈时间ms
	FeedbackStatus  string // 反馈状态 1 未处理 2已处理
	FeedbackResult  string // 反馈结果 1 无效反馈 2有效反馈
	CompleteTime    string // 处理时间ms
	CompleteRemark  string // 处理备注
	CompleteAccount string // 处理人
	CreateTime      string // 创建时间
	UpdateTime      string // 更新时间
	DeleteTime      string // 删除时间
}

// feedbackColumns holds the columns for the table feedback.
var feedbackColumns = FeedbackColumns{
	Id:              "id",
	FeedbackType:    "feedback_type",
	UserId:          "user_id",
	UserAccount:     "user_account",
	Images:          "images",
	Desc:            "desc",
	FeedbackTime:    "feedback_time",
	FeedbackStatus:  "feedback_status",
	FeedbackResult:  "feedback_result",
	CompleteTime:    "complete_time",
	CompleteRemark:  "complete_remark",
	CompleteAccount: "complete_account",
	CreateTime:      "create_time",
	UpdateTime:      "update_time",
	DeleteTime:      "delete_time",
}

// NewFeedbackDao creates and returns a new DAO object for table data access.
func NewFeedbackDao(handlers ...gdb.ModelHandler) *FeedbackDao {
	return &FeedbackDao{
		group:    "default",
		table:    "feedback",
		columns:  feedbackColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *FeedbackDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *FeedbackDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *FeedbackDao) Columns() FeedbackColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *FeedbackDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *FeedbackDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *FeedbackDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
