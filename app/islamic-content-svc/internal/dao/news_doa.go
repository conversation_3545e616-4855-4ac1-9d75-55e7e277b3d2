// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// newsDoaDao is the data access object for the table news_doa.
// You can define custom methods on it to extend its functionality as needed.
type newsDoaDao struct {
	*internal.NewsDoaDao
}

var (
	// NewsDoa is a globally accessible object for table news_doa operations.
	NewsDoa = newsDoaDao{internal.NewNewsDoaDao()}
)

// Add your custom methods and functionality below.
