// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// newsWiridDao is the data access object for the table news_wirid.
// You can define custom methods on it to extend its functionality as needed.
type newsWiridDao struct {
	*internal.NewsWiridDao
}

var (
	// NewsWirid is a globally accessible object for table news_wirid operations.
	NewsWirid = newsWiridDao{internal.NewNewsWiridDao()}
)

// Add your custom methods and functionality below.
