// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// hajiJadwalContentDao is the data access object for the table haji_jadwal_content.
// You can define custom methods on it to extend its functionality as needed.
type hajiJadwalContentDao struct {
	*internal.HajiJadwalContentDao
}

var (
	// HajiJadwalContent is a globally accessible object for table haji_jadwal_content operations.
	HajiJadwalContent = hajiJadwalContentDao{internal.NewHajiJadwalContentDao()}
)

// Add your custom methods and functionality below.
