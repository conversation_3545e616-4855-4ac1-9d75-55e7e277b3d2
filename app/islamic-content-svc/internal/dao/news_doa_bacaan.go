// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// newsDoaBacaanDao is the data access object for the table news_doa_bacaan.
// You can define custom methods on it to extend its functionality as needed.
type newsDoaBacaanDao struct {
	*internal.NewsDoaBacaanDao
}

var (
	// NewsDoaBacaan is a globally accessible object for table news_doa_bacaan operations.
	NewsDoaBacaan = newsDoaBacaanDao{internal.NewNewsDoaBacaanDao()}
)

// Add your custom methods and functionality below.
