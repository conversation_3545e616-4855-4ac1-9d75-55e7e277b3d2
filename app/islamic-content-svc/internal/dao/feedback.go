// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// feedbackDao is the data access object for the table feedback.
// You can define custom methods on it to extend its functionality as needed.
type feedbackDao struct {
	*internal.FeedbackDao
}

var (
	// Feedback is a globally accessible object for table feedback operations.
	Feedback = feedbackDao{internal.NewFeedbackDao()}
)

// Add your custom methods and functionality below.
