package islamic

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/service"
)

type ControllerFeedback struct {
	v1.UnimplementedFeedbackServiceServer
}

func (*ControllerFeedback) FeedbackAdd(ctx context.Context, req *v1.FeedbackAddReq) (res *v1.FeedbackAddRes, err error) {
	res, err = service.Feedback().FeedbackAdd(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
		return
	}
	res.Code = 200
	res.Msg = "success"
	return
}
