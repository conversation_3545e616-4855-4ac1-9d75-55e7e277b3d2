// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message HajiJadwalContent {
    uint64 Id             = 1; // 主键ID
    uint64 JadwalId       = 2; // 朝觐时间表ID，关联haji_jadwal.id
    uint32 LanguageId     = 3; // 语言ID: 0-中文, 1-英文, 2-印尼语
    string TimeInfo       = 4; // 手动输入的时间信息
    string EventSummary   = 5; // 事件简述
    string AdditionalInfo = 6; // 附加信息
    string ArticleText    = 7; // 文章详情（副文本）
    uint64 CreateTime     = 8; // 创建时间（毫秒时间戳）
    uint64 UpdateTime     = 9; // 更新时间（毫秒时间戳）
}