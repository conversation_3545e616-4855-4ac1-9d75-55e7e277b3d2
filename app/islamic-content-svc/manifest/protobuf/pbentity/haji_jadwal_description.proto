// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message HajiJadwalDescription {
    uint64 Id          = 1; // 主键ID
    int32  Year        = 2; // 朝觐年份
    uint32 LanguageId  = 3; // 语言ID: 0-中文, 1-英文, 2-印尼语
    string Description = 4; // 日程说明文字
}