// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message HajiJadwal {
    uint64 Id         = 1; // 主键ID
    int32  ItemNo     = 2; // 项目编号
    uint64 CreateTime = 3; // 创建时间（毫秒时间戳）
    uint64 UpdateTime = 4; // 更新时间（毫秒时间戳）
}