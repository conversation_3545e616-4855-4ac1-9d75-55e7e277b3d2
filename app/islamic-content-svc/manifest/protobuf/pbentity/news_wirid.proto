// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message NewsWirid {
    uint32 Id         = 1; //
    string Name       = 2; // 名称
    uint32 Bacaans    = 3; // 数量
    int64  CreateTime = 4; // 创建时间（注册时间）
    int64  UpdateTime = 5; // 更新时间，0代表创建后未更新
}