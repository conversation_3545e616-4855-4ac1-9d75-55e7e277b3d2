syntax = "proto3";

package islamic.v1;
option go_package = "halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1";

import "common/base.proto";
import "google/protobuf/wrappers.proto";

// 名言标签列表请求
message WisdomCateReq {
  uint32 language_id = 1;   // 语言id
}

// 名言标签列表数据
message WisdomCateListData {
  repeated WisdomCateItem list = 1;
}

// 名言标签列表响应
message WisdomCateRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  WisdomCateListData data = 4; // 名言标签列表
}


// 名言标签item
message WisdomCateItem {
  uint32 id         = 1;  // 分类ID
  int32  is_open    = 2;  // 是否启用
  int32  sort       = 3;  // 排序
  int32  cate_count = 4;  // 该分类下的数量
  string title      = 5;  // 分类名称
  uint32  language_id = 6;// 语言id
}



// 名言列表请求
message WisdomListReq {
  uint32 language_id = 1;   // 语言id
  uint32 cate_id = 2;       // 分类id
  string keyword = 3;       // 搜索关键字
  common.PageRequest page = 4;
}

message WisdomListData {
  repeated WisdomListItem list = 1;
  common.PageResponse page = 2;  // 分页参数
}

// 名言列表响应
message WisdomListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  WisdomListData data = 4; // 名言列表
}

// 名言item
message WisdomListItem {
  uint32 id         = 1;    // 问题ID
  int32  is_open    = 2;    // 是否启用
  int32  sort       = 3;    // 排序
  int32  views      = 4;    // 浏览次数
  string title      = 5;    // 图片标题
  string desc       = 6;    // 问题描述
  int32  language_id = 7;   // 语言id
  uint32 wisdom_cate_id = 8;   // 分类id
  string image_url = 9;     // 图片
}

// 名言详情请求
message WisdomOneReq {
  uint32 language_id = 1;   // 语言id
  uint32 id = 2;            // id
}

// 名言详情响应
message WisdomOneRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  WisdomOneItem data = 5;
}

// 名言详情item
message WisdomOneItem{
  uint32 article_id = 1; // 文章id
  uint32 language_id = 2;// 语言id
  string name = 3; // 文章标题
  string content = 4;  // 文章内容
  uint32 category_id  = 5;  // 分类id
  string category_name   = 6;  // 分类名称
  string cover_imgs   = 7;  // 专题图片
  string author      = 8; // 创建人
  int64  publish_time = 9; // 发布时间
  string  author_logo = 10; // 发布时间
  uint32  author_auth_status = 11; // 发布时间
}

// 名言服务定义
service WisdomService {
  // 名言分类列表
  rpc WisdomCateList(WisdomCateReq) returns (WisdomCateRes);
  // 名言列表
  rpc WisdomList(WisdomListReq) returns (WisdomListRes);
  // 名言详情
  rpc WisdomOne(WisdomOneReq) returns (WisdomOneRes);
}