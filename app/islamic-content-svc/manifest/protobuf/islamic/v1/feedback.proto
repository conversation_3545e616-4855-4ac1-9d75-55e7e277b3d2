syntax = "proto3";

package islamic.v1;
option go_package = "halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1";

import "common/base.proto";



message FeedbackAddReq {
  int32 feedback_type = 1; // 反馈类型 1-问题反馈,2-建议反馈
  string desc = 2;        // 反馈描述
  repeated FeedBackImages images = 3; // 反馈图片
}
message FeedBackImages {
  string url = 1;
  string key = 2;
  string name = 3;
}

message FeedbackAddRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}

service FeedbackService {
    rpc FeedbackAdd(FeedbackAddReq) returns (FeedbackAddRes);
}