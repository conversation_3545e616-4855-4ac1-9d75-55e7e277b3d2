-- 内容管理表 - 基础信息
DROP TABLE IF EXISTS `content_item`;
CREATE TABLE `content_item` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `item_no` INT COMMENT '内容编号',
    `content_type` TINYINT(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '内容类型: 0-文章, 1-视频, 2-音频, 3-图片',
    `icon_url` VARCHAR(500) COMMENT '图标URL',
    `thumbnail_url` VARCHAR(500) COMMENT '缩略图URL',
    `media_url` VARCHAR(500) COMMENT '媒体文件URL（视频/音频）',
    `status` TINYINT(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '状态: 0-草稿, 1-已发布, 2-已下架',
    `sort_order` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序权重',
    `view_count` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '浏览次数',
    `like_count` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '点赞次数',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    `publish_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '发布时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    INDEX `idx_item_no` (`item_no`) COMMENT '内容编号索引',
    INDEX `idx_content_type` (`content_type`) COMMENT '内容类型索引',
    INDEX `idx_status` (`status`) COMMENT '状态索引',
    INDEX `idx_sort_order` (`sort_order`) COMMENT '排序索引',
    INDEX `idx_publish_time` (`publish_time`) COMMENT '发布时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容管理基础信息表';

-- 内容管理表 - 多语言内容
DROP TABLE IF EXISTS `content_item_content`;
CREATE TABLE `content_item_content` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `content_id` BIGINT UNSIGNED NOT NULL COMMENT '内容ID，关联content_item.id',
    `language_id` TINYINT(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '语言ID: 0-中文, 1-英文, 2-印尼语',
    `title` VARCHAR(255) NOT NULL COMMENT '标题',
    `subtitle` VARCHAR(255) COMMENT '副标题',
    `summary` TEXT COMMENT '简介/摘要',
    `content_text` LONGTEXT COMMENT '正文内容',
    `tags` VARCHAR(500) COMMENT '标签（JSON格式）',
    `meta_keywords` VARCHAR(500) COMMENT 'SEO关键词',
    `meta_description` TEXT COMMENT 'SEO描述',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_content_language` (`content_id`, `language_id`) COMMENT '内容ID和语言唯一索引',
    INDEX `idx_content_id` (`content_id`) COMMENT '内容ID索引',
    INDEX `idx_language_id` (`language_id`) COMMENT '语言ID索引',
    INDEX `idx_title` (`title`) COMMENT '标题索引',
    FULLTEXT KEY `ft_content` (`title`, `summary`, `content_text`) COMMENT '全文搜索索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容管理多语言内容表';

-- 内容分类表
DROP TABLE IF EXISTS `content_category`;
CREATE TABLE `content_category` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `parent_id` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '父分类ID，0表示顶级分类',
    `category_code` VARCHAR(50) NOT NULL COMMENT '分类代码',
    `icon_url` VARCHAR(500) COMMENT '分类图标URL',
    `sort_order` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '排序权重',
    `status` TINYINT(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '状态: 0-禁用, 1-启用',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_category_code` (`category_code`) COMMENT '分类代码唯一索引',
    INDEX `idx_parent_id` (`parent_id`) COMMENT '父分类ID索引',
    INDEX `idx_sort_order` (`sort_order`) COMMENT '排序索引',
    INDEX `idx_status` (`status`) COMMENT '状态索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容分类表';

-- 内容分类多语言表
DROP TABLE IF EXISTS `content_category_content`;
CREATE TABLE `content_category_content` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `category_id` BIGINT UNSIGNED NOT NULL COMMENT '分类ID，关联content_category.id',
    `language_id` TINYINT(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '语言ID: 0-中文, 1-英文, 2-印尼语',
    `name` VARCHAR(100) NOT NULL COMMENT '分类名称',
    `description` TEXT COMMENT '分类描述',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_category_language` (`category_id`, `language_id`) COMMENT '分类ID和语言唯一索引',
    INDEX `idx_category_id` (`category_id`) COMMENT '分类ID索引',
    INDEX `idx_language_id` (`language_id`) COMMENT '语言ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容分类多语言表';

-- 内容与分类关联表
DROP TABLE IF EXISTS `content_category_relation`;
CREATE TABLE `content_category_relation` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `content_id` BIGINT UNSIGNED NOT NULL COMMENT '内容ID，关联content_item.id',
    `category_id` BIGINT UNSIGNED NOT NULL COMMENT '分类ID，关联content_category.id',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_content_category` (`content_id`, `category_id`) COMMENT '内容和分类唯一索引',
    INDEX `idx_content_id` (`content_id`) COMMENT '内容ID索引',
    INDEX `idx_category_id` (`category_id`) COMMENT '分类ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容分类关联表';

-- 插入示例分类数据
INSERT INTO `content_category` (`parent_id`, `category_code`, `sort_order`, `status`) VALUES
(0, 'islamic_knowledge', 1, 1),
(0, 'prayer_guidance', 2, 1),
(0, 'quran_study', 3, 1),
(0, 'hadith_collection', 4, 1),
(0, 'islamic_history', 5, 1);

-- 插入示例分类多语言内容
INSERT INTO `content_category_content` (`category_id`, `language_id`, `name`, `description`) VALUES
(1, 0, '伊斯兰知识', '伊斯兰教基础知识和教义'),
(1, 1, 'Islamic Knowledge', 'Basic Islamic knowledge and teachings'),
(1, 2, 'Pengetahuan Islam', 'Pengetahuan dasar dan ajaran Islam'),
(2, 0, '祈祷指导', '祈祷方法和指导'),
(2, 1, 'Prayer Guidance', 'Prayer methods and guidance'),
(2, 2, 'Panduan Shalat', 'Metode dan panduan shalat'),
(3, 0, '古兰经学习', '古兰经研读和理解'),
(3, 1, 'Quran Study', 'Quran study and understanding'),
(3, 2, 'Studi Al-Quran', 'Studi dan pemahaman Al-Quran'),
(4, 0, '圣训集', '先知穆罕默德的圣训'),
(4, 1, 'Hadith Collection', 'Hadith of Prophet Muhammad'),
(4, 2, 'Koleksi Hadits', 'Hadits Nabi Muhammad'),
(5, 0, '伊斯兰历史', '伊斯兰教历史和文化'),
(5, 1, 'Islamic History', 'Islamic history and culture'),
(5, 2, 'Sejarah Islam', 'Sejarah dan budaya Islam');
