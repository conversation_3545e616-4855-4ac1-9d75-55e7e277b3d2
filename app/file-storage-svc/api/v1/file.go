package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type FileUploadReq struct {
	g.Meta `path:"/upload" method:"post" mime:"multipart/form-data" tags:"工具" summary:"上传文件"`
	File   *ghttp.UploadFile `json:"file" type:"file" dc:"选择上传文件"`
}

type FileUploadRes struct {
	Name       string `json:"name" dc:"图片的名称"`
	Key        string `json:"key" dc:"对象KEY"`
	BackendUrl string `json:"url" dc:"图片的后台访问地址"`
}

type GetBackUpUrlReq struct {
	Name string `json:"name" dc:"图片的名称"`
}

type GetBackUpUrlRes struct {
	Name string `json:"name" dc:"图片的名称"`
}

type MultiUploadReq struct { // 批量上传 // multiple
	g.Meta `path:"/multi_upload" method:"post" mime:"multipart/form-data" tags:"工具" summary:"多个上传文件"`
	Files  []*ghttp.UploadFile `json:"files" type:"file" dc:"选择上传文件"`
}

type MultiUploadRes struct {
	List []*FileUploadRes
}

//type SetPrivateReq struct {
//	g.Meta `path:"/set_private" method:"post"  tags:"工具" summary:"设置目录为私有"`
//	Path   string `json:"path" dc:"目录"`
//}
//
//type SetPrivateRes struct {
//}
