package middleware

import (
	"encoding/json"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/i18n/gi18n"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/glog"
	"halalplus/app/file-storage-svc/internal/service"
	"net/http"
	"strings"
	"unsafe"
)

type (
	sMiddleware struct{}
)

func init() {
	service.RegisterMiddleware(New())
}

func New() service.IMiddleware {
	return &sMiddleware{}
}

// 2.5版本兼容:Ctx传播给异步流程或者保持和之前逻辑兼容
func (s *sMiddleware) NeverDoneCtx(r *ghttp.Request) {
	r.SetCtx(r.GetNeverDoneCtx())
	r.Middleware.Next()
}

// 记录访问请求头，请求体
func (s *sMiddleware) LogRequest(r *ghttp.Request) {
	// g.Log().Info(r.Context(), g.Map{"uid": 100, "name": "john"}) //或者Print，Debug，Info
	g.Log().Line().Debug(r.Context(), "\n\n")
	g.Log().Line().Debug(r.Context(), "--------------------------------------------------")
	ip := r.GetClientIp()
	g.Log().Line().Debug(r.Context(), ip, ",", r.RemoteAddr, ",", r.Header.Get("X-Real-IP"), ",", r.Header.Get("X-Forwarded-For"))
	g.Log().Line().Debug(r.Context(), r.Method, r.URL)
	g.Log().Line().Debug(r.Context(), r.Header)

	var bodyBytes []byte
	if r.Body != nil {
		bodyBytes = r.GetBody()
		tmpFile := r.GetUploadFile("file")
		if strings.HasSuffix(r.URL.Path, "/file") || tmpFile != nil {
			if r.PostForm == nil {
				bodyBytes = make([]byte, 0)
			} else {
				bodyBytes, _ = json.Marshal(r.PostForm)
			}
		}
	}
	if len(bodyBytes) > 0 {
		g.Log().Line().Debug(r.Context(), "Request.Body:", unsafe.String(unsafe.SliceData(bodyBytes), len(bodyBytes)))
	}
	g.Log().Line().Debug(r.Context(), "--------------------------------------------------")

	r.Middleware.Next()
}

func (s *sMiddleware) HandlerResponse(r *ghttp.Request) {
	r.Middleware.Next()

	// There's custom buffer content, it then exits current handler.
	if r.Response.BufferLength() > 0 {
		return
	}

	var (
		msg  string
		err  = r.GetError()
		res  = r.GetHandlerResponse()
		code = gerror.Code(err)
	)
	if err != nil {
		if code == gcode.CodeNil {
			code = gcode.CodeInternalError
		}
		if code.Code() == gcode.CodeDbOperationError.Code() { // 不返回SQL错误信息
			msg = gi18n.T(r.Context(), "internal.error")
		} else {
			msg = err.Error()
		}
	} else {
		if r.Response.Status > 0 && r.Response.Status != http.StatusOK {
			msg = http.StatusText(r.Response.Status)
			switch r.Response.Status {
			case http.StatusNotFound:
				code = gcode.CodeNotFound
			case http.StatusForbidden:
				code = gcode.CodeNotAuthorized
			default:
				code = gcode.CodeUnknown
			}
			// It creates error as it can be retrieved by other middlewares.
			err = gerror.NewCode(code, msg)
			r.SetError(err)
		} else if r.Response.Status == http.StatusOK {
			code = gcode.New(200, "OK", nil)
			msg = http.StatusText(r.Response.Status)
		}
	}

	lev := g.Log().GetLevel()
	deb := glog.LEVEL_DEBU
	if lev&deb > 1 {
		by, _ := gjson.Marshal(r.GetHandlerResponse())
		g.Log().Debug(r.Context(), "Response = ", string(by))
	}

	// r.Response.DefaultCORSOptions()
	// s.handleSensitiveFields(r, res) // 回包处理脱敏字段
	// s.maskResponseFields(r, res) // 回包处理脱敏字段
	//s.maskResponseFields(r, res) // 回包处理脱敏字段
	r.Response.WriteJson(ghttp.DefaultHandlerResponse{
		Code:    code.Code(),
		Message: msg,
		Data:    res,
	})
}
func (s *sMiddleware) SetCORSOptions(r *ghttp.Request) {
	corsOpts := r.Response.DefaultCORSOptions()
	// corsOpts.AllowDomain = []string{"http://192.168.10.34:5173"}
	// corsOpts.AllowOrigin = "http://192.168.10.34:5173"
	r.Response.CORS(corsOpts)
	r.Middleware.Next()
}

func ConvertLowercase(in string) (out string) {
	fieldPaths := strings.Split(in, "-")
	length := len(fieldPaths)
	for i, v := range fieldPaths {
		if len(v) == 0 {
			return
		}
		lowVal := strings.ToLower(v[:1]) + v[1:]
		out += lowVal
		if i != length-1 {
			out += "-"
		}
	}

	return
}
