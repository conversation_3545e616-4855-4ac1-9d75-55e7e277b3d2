// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/user-account-svc/api/pbentity";



message UserSigninLog {
    uint32 BigintUnsigned  = 1;  //
    uint32 UserId          = 2;  // 会员id
    int32  VipLevel        = 3;  // 会员当时vip等级
    int64  SigninTime      = 4;  // 登录时间
    string Ip              = 5;  // 登录ip（ip6长度为39字符）
    string IpRegion        = 6;  // ip地址位置
    string DeviceId        = 7;  // 设备编号
    string DeviceOs        = 8;  // 设备系统（ios，android，mac，windows，。。。）
    string DeviceOsVersion = 9;  // 设备系统版本号
    string DeviceType      = 10; // 设备类型（mobile手机，desktop台式，pad平板，。。。其他）
    string AppType         = 11; // 应用类型（1:android 2: ios，3:h5，4:web，5:其他）
    string AppVersion      = 12; // 应用版本号
    string Host            = 13; // 登录域名
}