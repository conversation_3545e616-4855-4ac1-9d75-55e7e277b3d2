// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/user-account-svc/api/pbentity";



message UserSignupLog {
    uint64 Id              = 1;  //
    uint64 UserId          = 2;  // 关联 user 表主键
    string Ip              = 3;  // 注册ip
    string IpRegion        = 4;  // 注册IP地理区域
    string DeviceId        = 5;  // 注册设备号（设备指纹）
    string DeviceOs        = 6;  // 注册设备系统（android,ios,windows,mac,...）
    string DeviceOsVersion = 7;  // 注册设备系统版本号
    int32  DeviceType      = 8;  // 注册设备类型（1:mobile手机，2:desktop台式，3:pad平板，。。。其他）
    int32  AppType         = 9;  // 应用类型（1:android 2: ios，3:h5，4:web，5:其他）
    string AppVersion      = 10; // 注册应用类型版本号
    int64  CreateTime      = 11; // 创建时间（注册时间）
    int64  UpdateTime      = 12; // 更新时间，0代表创建后未被修改过（哪些字段的更新会触发这个？）
}