// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/user-account-svc/api/pbentity";



message UserSession {
    uint64 Id             = 1;  // 会话记录ID
    string SessionId      = 2;  // 会话唯一ID（绑定 JWT 的 sid 字段）
    string SecretKey      = 3;  // refresh token的密钥
    uint64 UserId         = 4;  // 会员ID
    int64  LoginTime      = 5;  // 登录时间（时间戳）
    uint64 ExpireTime     = 6;  // 会话过期时间（时间戳）
    int64  LogoutTime     = 7;  // 登出时间（0表示未登出）
    string Ip             = 8;  // 登录IP
    string DeviceId       = 9;  // 设备ID
    string DeviceOs       = 10; // 设备系统
    string DeviceType     = 11; // 设备类型（mobile/desktop/pad等）
    string AppType        = 12; // 应用类型（android/ios/h5/web等）
    string AppVersion     = 13; // 应用版本
    string PushToken      = 14; // 推送token，对应 Android（如 FCM）和 iOS（如 APNs）系统
    int32  IsOnline       = 15; // 是否在线（1=在线，0=离线）
    int32  IsKicked       = 16; // 是否被踢下线（1=是，0=否）
    int64  LastActiveTime = 17; // 最近活跃时间（如访问接口时间）
}