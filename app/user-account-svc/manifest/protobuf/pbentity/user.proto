// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/user-account-svc/api/pbentity";



message User {
    uint64 Id               = 1;  //
    string Account          = 2;  // 账号
    string Password         = 3;  // 密码
    string AreaCode         = 4;  // 手机国际区号，如：86
    string PhoneNum         = 5;  // 手机号
    int32  IsBanned         = 6;  // 账号封号状态： 1 正常 2 封号
    string SecurityPassword = 7;  // 安全密码，修改个人绑定信息时要验证
    int32  IsTest           = 8;  // 测试账号： 1 是 ，其他值：否
    int64  CreateTime       = 9;  // 创建时间（注册时间）
    int64  UpdateTime       = 10; // 更新时间，0代表创建后未被修改过
}