// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/user-account-svc/api/pbentity";



message UserKvData {
    uint64 Id         = 1; // 唯一标识，自增主键
    uint64 UserId     = 2; // 所属用户的唯一 ID
    string KeyPath    = 3; // 以 dot 分隔的键路径，例如 "profile.theme.color"
    string ValueData  = 4; // 对应键的 JSON 数据值，支持任意结构
    int32  ValueSize  = 5; // value_data 的 UTF-8 字节长度，用于配额限制
    int64  CreateTime = 6; // 创建时间
    int64  UpdateTime = 7; // 最后更新时间
}