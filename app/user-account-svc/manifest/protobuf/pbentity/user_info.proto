// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/user-account-svc/api/pbentity";



message UserInfo {
    uint64 Id                   = 1;  //
    uint64 UserId               = 2;  // 关联 user 表主键
    string Email                = 3;  // 邮箱地址
    int64  BindEmailTime        = 4;  // 邮箱绑定时间
    int64  BindRealNameTime     = 5;  // 真实姓名绑定时间
    int64  BindPhoneTime        = 6;  // 手机号绑定时间
    string PhoneNum             = 7;  // 手机号
    int64  PasswordModifyTime   = 8;  // 登录密码支付密码最近修改时间
    uint32 CountryId            = 9;  // 国家id
    string Language             = 10; // 语言 : zh-CN:中文, id:Indonesian, en:English
    int32  YearOfBirth          = 11; // 出生年
    int32  MonthOfBirth         = 12; // 出生月
    int32  DayOfBirth           = 13; // 出生日
    string Avatar               = 14; // 头像url
    string Gender               = 15; // 性别：0未知 1男 2女
    string Nickname             = 16; // 昵称
    int64  NicknameModifyTime   = 17; // 昵称最近一次修改时间
    string FirstName            = 18; // 第一个名字
    string MiddleName           = 19; // 中间名字
    string LastName             = 20; // 最后一个名字
    int64  Version              = 21; // 该记录的版本号
    string IdentityCard         = 22; // 身份证号码
    string IdentityCardImgs     = 23; // 身份证图片
    string Contact              = 24; // 社交联系方式 wechat qq等
    string Address              = 25; // 住址
    string CreateAccount        = 26; // 创建者账号
    int32  CreateType           = 27; // 创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）
    string UpdateAccount        = 28; // 更新者账号
    int32  UpdateType           = 29; // 更新者来源
    int32  DataType             = 30; // 数据类型:1正式数据;2测试数据
    int32  Source               = 31; // 注册来源( 1直客，2代理，3邀请，4后台）
    int32  IsOnline             = 32; // 是否在线：1是 2 否
    int32  SigninCount          = 33; // 登录次数
    int64  LastSigninTime       = 34; // 最后一次登录时间
    string LastSigninIp         = 35; // 最后登录ip
    string LastSigninDeviceId   = 36; // 最后登录设备号
    int32  LastSigninAppType    = 37; // 最近登录应用类型（1:android 2: ios，3:h5，4:web，5:其他）
    string LastSigninAppVersion = 38; // 最近登录应用类型版本号
    int64  CreateTime           = 39; // 创建时间
    int64  UpdateTime           = 40; // 更新时间
}