// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/user-account-svc/api/pbentity";



message UserAppSettings {
    uint64 Id                 = 1;  //
    string Account            = 2;  // 账号
    uint32 CountryId          = 3;  // 国家id
    string Currency           = 4;  // 币种
    string Language           = 5;  // 语言 : zh-CN:中文, id:Indonesian, en:English
    int32  IsVibrationOpen    = 6;  // 振动设置 开关状态：1开, 2关
    int32  YearOfBirth        = 7;  // 出生年
    int32  MonthOfBirth       = 8;  // 出生月
    int32  DayOfBirth         = 9;  // 出生日
    int32  IsRemindVoiceOpen  = 10; // 推送提示-声音：1开, 2关
    int32  IsRemindVoiceType  = 11; // 推送提示-声音类型：1欢呼声, 2哨声
    int32  IsRemindShockOpen  = 12; // 推送提示-震动：1开, 2关
    int32  IsWithdrawAuth     = 13; // 提现验证：1开, 2关
    string Avatar             = 14; // 头像url
    string Gender             = 15; // 性别：0未知 1男 2女
    string Nickname           = 16; // 昵称
    int64  NicknameModityTime = 17; // 昵称最近一次修改时间
    string FirstName          = 18; // 第一个名字
    string MiddleName         = 19; // 中间名字
    string LastName           = 20; // 最后一个名字
    int64  Version            = 21; // 该记录的版本号
    string IdentityCard       = 22; // 身份证号码
    string IdentityCardImgs   = 23; // 身份证图片
    string Contact            = 24; // 社交联系方式 wechat qq等
    string Address            = 25; // 住址
    string CreateAccount      = 26; // 创建者账号
    int32  CreateType         = 27; // 创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）
    string UpdateAccount      = 28; // 更新者账号
    int32  UpdateType         = 29; // 更新者来源
    int64  CreateTime         = 30; // 创建时间
    int64  UpdateTime         = 31; // 更新时间
}